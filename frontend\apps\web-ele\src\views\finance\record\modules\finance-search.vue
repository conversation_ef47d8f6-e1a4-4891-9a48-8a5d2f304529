<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import {
  ElCard,
  ElCollapse,
  ElCollapseItem,
  ElForm,
  ElFormItem,
  ElRow,
  ElCol,
  ElInput,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElButton,
  ElSpace
} from 'element-plus';
import type { CollapseModelValue } from 'element-plus';
import { fetchOfflineStores, fetchIncomeCategories, fetchExpenditureCategories } from '../../../../api/finance';
import type { Store, Category } from '../../../../api/finance';
import { useDataScope } from '../../../../composables/useDataScope';

defineOptions({ name: 'FinanceSearch' });

// 定义props，v-model:model类型为{dateRange, shop, type, category}
interface SearchModel {
  dateRange: string[];
  shop: string;
  type: string;
  category: string;
}
const model = defineModel<SearchModel>('model', { required: true });

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
  (e: 'collapse-change', val: boolean): void;
}
const emit = defineEmits<Emits>();

const isCollapse = ref(false); // 是否折叠

// 数据范围管理
const { filterAccessibleStoreOptions, isDataScopesLoaded } = useDataScope();

// 店铺选项
const storeOptions = ref<Array<{ label: string; value: string }>>([]);
// 分类选项
const categoryOptions = ref<Array<{ label: string; value: string }>>([]);
// 存储原始店铺选项
const allStoreOptions = ref([]);

// 根据类型计算分类选项
const filteredCategoryOptions = computed(() => {
  return categoryOptions.value;
});

function reset() {
  emit('reset');
}

function search() {
  emit('search');
}

function handleCollapseChange(val: CollapseModelValue) {
  // val 可能为 string 或 string[]
  let isCollapsed = true;
  if (Array.isArray(val)) {
    isCollapsed = !val.includes('order-search');
  } else if (typeof val === 'string') {
    isCollapsed = val !== 'order-search';
  }
  isCollapse.value = isCollapsed;
  emit('collapse-change', isCollapsed);
}

// 加载店铺选项
async function loadStoreOptions() {
  try {
    const response = await fetchOfflineStores();
    if (response && response.data) {
      allStoreOptions.value = [
        { label: '全部', value: '' }, // 添加全部选项
        ...response.data
          .filter(store => store.status === 1) // 只显示启用的店铺
          .map(store => ({
            label: store.name,
            value: store.name
          }))
      ];

      // 应用数据范围过滤
      applyDataScopeFilter();
    }
  } catch (error) {
    console.error('加载店铺选项失败:', error);
  }
}

// 应用数据范围过滤
function applyDataScopeFilter() {
  if (allStoreOptions.value.length > 0) {
    // 根据用户数据范围过滤店铺选项
    storeOptions.value = filterAccessibleStoreOptions(allStoreOptions.value, 'offline');
  }
}

// 加载分类选项
async function loadCategoryOptions() {
  try {
    const type = model.value.type;
    if (!type) {
      categoryOptions.value = [{ label: '全部', value: '' }];
      return;
    }

    let response;
    if (type === '收入') {
      response = await fetchIncomeCategories();
    } else if (type === '支出') {
      response = await fetchExpenditureCategories();
    }

    if (response && response.data) {
      categoryOptions.value = [
        { label: '全部', value: '' }, // 添加全部选项
        ...response.data
          .filter(category => category.status === 1) // 只显示启用的分类
          .map(category => ({
            label: category.name,
            value: category.name
          }))
      ];
    }
  } catch (error) {
    console.error('加载分类选项失败:', error);
  }
}

// 监听类型变化，重新加载分类选项
watch(() => model.value.type, (newType) => {
  // 清空分类选择
  model.value.category = '';
  // 重新加载分类选项
  loadCategoryOptions();
});

// 监听数据范围加载状态
watch(isDataScopesLoaded, (loaded) => {
  if (loaded) {
    applyDataScopeFilter();
  }
});

// 组件挂载时加载选项
onMounted(() => {
  loadStoreOptions();
  loadCategoryOptions();
});
</script>

<template>
  <ElCard class="card-wrapper">
    <ElCollapse @change="handleCollapseChange">
      <ElCollapseItem title="搜索" name="order-search">
        <ElForm :model="model" label-position="right" :label-width="80">
          <ElRow :gutter="24">
            <!-- 日期范围 -->
            <ElCol :lg="6" :md="12" :sm="24">
              <ElFormItem label="日期" prop="dateRange">
                <ElDatePicker
                  v-model="model.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  clearable
                  style="width: 100%"
                />
              </ElFormItem>
            </ElCol>
            <!-- 店铺 -->
            <ElCol :lg="5" :md="12" :sm="24">
              <ElFormItem label="店铺" prop="shop">
                <ElSelect v-model="model.shop" placeholder="请选择店铺" style="width: 100%" clearable filterable>
                  <ElOption
                    v-for="option in storeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <!-- 类型 -->
            <ElCol :lg="5" :md="12" :sm="24">
              <ElFormItem label="类型" prop="type">
                <ElSelect v-model="model.type" placeholder="请选择类型" clearable style="width: 100%">
                  <ElOption label="收入" value="收入" />
                  <ElOption label="支出" value="支出" />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <!-- 分类 -->
            <ElCol :lg="5" :md="12" :sm="24">
              <ElFormItem label="分类" prop="category">
                <ElSelect v-model="model.category" placeholder="请选择分类" style="width: 100%" clearable filterable>
                  <ElOption
                    v-for="option in filteredCategoryOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <!-- 操作按钮 -->
            <ElCol :lg="3" :md="12" :sm="24" class="flex justify-end items-center">
              <ElSpace>
                <ElButton @click="reset">重置</ElButton>
                <ElButton type="primary" plain @click="search">搜索</ElButton>
              </ElSpace>
            </ElCol>
          </ElRow>
        </ElForm>
      </ElCollapseItem>
    </ElCollapse>
  </ElCard>
</template>

<style scoped>
.card-wrapper {
  margin-bottom: 0;
}

.card-wrapper :deep(.el-card__body) {
  padding: 16px;
}

.card-wrapper :deep(.el-collapse) {
  border: none;
}

.card-wrapper :deep(.el-collapse-item__header) {
  background-color: transparent;
  border: none;
  padding: 0 0 12px 0;
  font-weight: 500;
  font-size: 14px;
}

.card-wrapper :deep(.el-collapse-item__wrap) {
  border: none;
  background-color: transparent;
}

.card-wrapper :deep(.el-collapse-item__content) {
  padding: 0;
  background-color: transparent;
}

/* 确保动画效果正确 */
.card-wrapper :deep(.el-collapse-item) {
  border: none;
}

.card-wrapper :deep(.el-collapse-item__arrow) {
  margin: 0 8px 0 0;
}

/* 表单样式优化 */
.card-wrapper :deep(.el-form-item) {
  margin-bottom: 16px;
}

.card-wrapper :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

/* 日期选择器样式 */
.card-wrapper :deep(.el-date-editor) {
  width: 100%;
}

/* 按钮样式保持默认 */

/* 输入框样式 */
.card-wrapper :deep(.el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.3s;
}

.card-wrapper :deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
