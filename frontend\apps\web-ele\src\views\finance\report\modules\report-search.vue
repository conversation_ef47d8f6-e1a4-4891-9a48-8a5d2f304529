<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import {
  ElCard,
  ElCollapse,
  ElCollapseItem,
  ElForm,
  ElFormItem,
  ElRow,
  ElCol,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElButton,
  ElSpace
} from 'element-plus';
import type { CollapseModelValue } from 'element-plus';
import { fetchOfflineStores, fetchIncomeCategories, fetchExpenditureCategories } from '../../../../api/finance';
import { useDataScope } from '../../../../composables/useDataScope';

defineOptions({ name: 'FinanceSearch' });

// 定义props，v-model:model类型为{dateRange, shop, type, category}
interface SearchModel {
  dateRange: string[];
  shop: string;
  type: string;
  category: string;
}
const model = defineModel<SearchModel>('model', { required: true });

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
  (e: 'collapse-change', val: boolean): void;
}
const emit = defineEmits<Emits>();

const isCollapse = ref(false); // 是否折叠
const activeNames = ref(['finance-search']); // 默认展开搜索面板

// 数据范围管理
const { filterAccessibleStoreOptions, isDataScopesLoaded } = useDataScope();

// 店铺选项
const storeOptions = ref<Array<{ label: string; value: string }>>([]);
// 分类选项
const categoryOptions = ref<Array<{ label: string; value: string }>>([]);
// 存储原始店铺选项
const allStoreOptions = ref([]);

// 根据类型计算分类选项
const filteredCategoryOptions = computed(() => {
  return categoryOptions.value;
});

function reset() {
  emit('reset');
}

function search() {
  emit('search');
}

// 快捷时间选择
function selectDateRange(days: number | 'all') {
  if (days === 'all') {
    // 全部：清空日期范围
    model.value.dateRange = [];
    search();
    return;
  }

  const endDate = new Date();
  const startDate = new Date();
  startDate.setDate(endDate.getDate() - (days - 1));

  const startDateStr = startDate.toISOString().split('T')[0];
  const endDateStr = endDate.toISOString().split('T')[0];

  if (startDateStr && endDateStr) {
    model.value.dateRange = [startDateStr, endDateStr];
    // 自动触发搜索
    search();
  }
}

// 计算当前选中的快捷日期范围
const selectedQuickRange = computed(() => {
  // 如果没有日期范围，表示选择了"全部"
  if (!model.value.dateRange || model.value.dateRange.length === 0) {
    return 'all';
  }

  if (model.value.dateRange.length !== 2) {
    return null;
  }

  const [startDate, endDate] = model.value.dateRange;
  if (!startDate || !endDate) {
    return null;
  }

  const start = new Date(startDate);
  const end = new Date(endDate);

  // 计算日期差
  const diffTime = end.getTime() - start.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;

  // 检查是否是今天结束的日期范围
  const today = new Date();
  const todayStr = today.toISOString().split('T')[0];

  if (endDate === todayStr) {
    if (diffDays === 7) return 7;
    if (diffDays === 30) return 30;
    if (diffDays === 90) return 90;
    if (diffDays === 180) return 180;
    if (diffDays === 360) return 360;
  }

  return null;
});

function handleCollapseChange(val: CollapseModelValue) {
  // val 可能为 string 或 string[]
  let isCollapsed = true;
  if (Array.isArray(val)) {
    isCollapsed = !val.includes('order-search');
  } else if (typeof val === 'string') {
    isCollapsed = val !== 'order-search';
  }
  isCollapse.value = isCollapsed;
  emit('collapse-change', isCollapsed);
}

// 加载店铺选项
async function loadStoreOptions() {
  try {
    const response = await fetchOfflineStores();
    if (response && response.data) {
      allStoreOptions.value = [
        { label: '全部', value: '' }, // 添加全部选项
        ...response.data
          .filter(store => store.status === 1) // 只显示启用的店铺
          .map(store => ({
            label: store.name,
            value: store.name
          }))
      ];

      // 应用数据范围过滤
      applyDataScopeFilter();
    }
  } catch (error) {
    console.error('加载店铺选项失败:', error);
  }
}

// 应用数据范围过滤
function applyDataScopeFilter() {
  if (allStoreOptions.value.length > 0) {
    // 根据用户数据范围过滤店铺选项
    const filteredOptions = filterAccessibleStoreOptions(allStoreOptions.value, 'offline');
    storeOptions.value = filteredOptions;
  }
}

// 加载分类选项
async function loadCategoryOptions() {
  try {
    const type = model.value.type;
    if (!type) {
      categoryOptions.value = [{ label: '全部', value: '' }];
      return;
    }

    let response;
    if (type === '收入') {
      response = await fetchIncomeCategories();
    } else if (type === '支出') {
      response = await fetchExpenditureCategories();
    }

    if (response && response.data) {
      categoryOptions.value = [
        { label: '全部', value: '' }, // 添加全部选项
        ...response.data
          .filter(category => category.status === 1) // 只显示启用的分类
          .map(category => ({
            label: category.name,
            value: category.name
          }))
      ];
    }
  } catch (error) {
    console.error('加载分类选项失败:', error);
  }
}

// 监听类型变化，重新加载分类选项
watch(() => model.value.type, () => {
  // 清空分类选择
  model.value.category = '';
  // 重新加载分类选项
  loadCategoryOptions();
});

// 监听数据范围加载状态
watch(isDataScopesLoaded, (loaded) => {
  if (loaded) {
    applyDataScopeFilter();
  }
});

// 组件挂载时加载选项
onMounted(() => {
  loadStoreOptions();
  loadCategoryOptions();
});
</script>

<template>
  <ElCard class="card-wrapper">
    <ElCollapse v-model="activeNames" @change="handleCollapseChange">
      <ElCollapseItem title="搜索" name="finance-search">
        <ElForm :model="model" label-position="right" :label-width="80">
          <ElRow :gutter="24">
            <!-- 日期范围 -->
            <ElCol :lg="9" :md="12" :sm="24">
              <ElFormItem label="日期" prop="dateRange">
                <ElDatePicker
                  v-model="model.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  clearable
                  style="width: 100%"
                />
                <!-- 快捷时间选择按钮 -->
                <div style="margin-top: 8px; display: flex; flex-wrap: nowrap; gap: 6px; overflow-x: auto;">
                  <ElButton
                    size="small"
                    @click="selectDateRange(7)"
                    :type="selectedQuickRange === 7 ? 'primary' : 'default'"
                    :plain="selectedQuickRange !== 7"
                  >
                    最近7天
                  </ElButton>
                  <ElButton
                    size="small"
                    @click="selectDateRange(30)"
                    :type="selectedQuickRange === 30 ? 'primary' : 'default'"
                    :plain="selectedQuickRange !== 30"
                  >
                    最近30天
                  </ElButton>
                  <ElButton
                    size="small"
                    @click="selectDateRange(90)"
                    :type="selectedQuickRange === 90 ? 'primary' : 'default'"
                    :plain="selectedQuickRange !== 90"
                  >
                    最近90天
                  </ElButton>
                  <ElButton
                    size="small"
                    @click="selectDateRange(180)"
                    :type="selectedQuickRange === 180 ? 'primary' : 'default'"
                    :plain="selectedQuickRange !== 180"
                  >
                    最近180天
                  </ElButton>
                  <ElButton
                    size="small"
                    @click="selectDateRange(360)"
                    :type="selectedQuickRange === 360 ? 'primary' : 'default'"
                    :plain="selectedQuickRange !== 360"
                  >
                    最近360天
                  </ElButton>
                  <ElButton
                    size="small"
                    @click="selectDateRange('all')"
                    :type="selectedQuickRange === 'all' ? 'primary' : 'default'"
                    :plain="selectedQuickRange !== 'all'"
                  >
                    全部
                  </ElButton>
                </div>
              </ElFormItem>
            </ElCol>
            <!-- 店铺 -->
            <ElCol :lg="4" :md="12" :sm="24">
              <ElFormItem label="店铺" prop="shop">
                <ElSelect v-model="model.shop" placeholder="请选择店铺" style="width: 100%" clearable filterable>
                  <ElOption
                    v-for="option in storeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <!-- 类型 -->
            <ElCol :lg="4" :md="12" :sm="24">
              <ElFormItem label="类型" prop="type">
                <ElSelect v-model="model.type" placeholder="请选择类型" clearable style="width: 100%">
                  <ElOption label="收入" value="收入" />
                  <ElOption label="支出" value="支出" />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <!-- 分类 -->
            <ElCol :lg="4" :md="12" :sm="24">
              <ElFormItem label="分类" prop="category">
                <ElSelect v-model="model.category" placeholder="请选择分类" style="width: 100%" clearable filterable>
                  <ElOption
                    v-for="option in filteredCategoryOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <!-- 操作按钮 -->
            <ElCol :lg="3" :md="12" :sm="24" class="flex justify-end items-center">
              <ElSpace>
                <ElButton @click="reset">重置</ElButton>
                <ElButton type="primary" plain @click="search">搜索</ElButton>
              </ElSpace>
            </ElCol>
          </ElRow>
        </ElForm>
      </ElCollapseItem>
    </ElCollapse>
  </ElCard>
</template>

<style scoped>
.card-wrapper {
  margin-bottom: 0;
}

.card-wrapper :deep(.el-card__body) {
  padding: 16px;
}

.card-wrapper :deep(.el-collapse) {
  border: none;
}

.card-wrapper :deep(.el-collapse-item__header) {
  background-color: transparent;
  border: none;
  padding: 0 0 12px 0;
  font-weight: 500;
  font-size: 14px;
}

.card-wrapper :deep(.el-collapse-item__wrap) {
  border: none;
  background-color: transparent;
}

.card-wrapper :deep(.el-collapse-item__content) {
  padding: 0;
  background-color: transparent;
}

/* 确保动画效果正确 */
.card-wrapper :deep(.el-collapse-item) {
  border: none;
}

.card-wrapper :deep(.el-collapse-item__arrow) {
  margin: 0 8px 0 0;
}

/* 表单样式优化 */
.card-wrapper :deep(.el-form-item) {
  margin-bottom: 16px;
}

.card-wrapper :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

/* 日期选择器样式 */
.card-wrapper :deep(.el-date-editor) {
  width: 100%;
}

/* 按钮样式保持默认 */

/* 输入框样式 */
.card-wrapper :deep(.el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.3s;
}

.card-wrapper :deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
