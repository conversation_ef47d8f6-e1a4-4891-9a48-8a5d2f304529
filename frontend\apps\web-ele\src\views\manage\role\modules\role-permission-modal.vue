<script setup lang="ts">
import { ref, watch, computed } from 'vue';
import {
  ElMessage,
  ElDialog,
  ElTree,
  ElButton,
  ElLoading,
  ElTabs,
  ElTabPane,
  ElCheckboxGroup,
  ElCheckbox,
  ElDivider
} from 'element-plus';
import { fetchRolePermissions, assignRolePermissions, fetchRoleDataScopes, setRoleDataScopes, type DataScope } from '../../../../api/role';
import { fetchAllPermissions } from '../../../../api/permission';
import { fetchOfflineStores } from '../../../../api/finance';

// 弹窗可见性
const visible = defineModel<boolean>('visible', { default: false });

// Props
interface Props {
  roleData: {
    roleId: number;
    roleName: string;
  } | null;
}

const props = defineProps<Props>();

// 权限树数据
const permissionTreeData = ref<any[]>([]);
const loading = ref(false);
const treeRef = ref();

// 当前角色已有的权限
const currentPermissions = ref<number[]>([]);

// 数据范围相关
const storeOptions = ref<Array<{id: number, name: string}>>([]);
const selectedStores = ref<number[]>([]);
const currentDataScopes = ref<DataScope[]>([]);

// 弹窗标题
const title = computed(() => {
  return props.roleData ? `配置角色"${props.roleData.roleName}"的权限` : '权限配置';
});

// 权限树配置
const treeProps = {
  children: 'children',
  label: 'name',
  disabled: 'disabled'
};

// 监听弹窗显示，加载权限数据
watch(visible, async (val) => {
  if (val && props.roleData) {
    await loadPermissions();
    await loadStoreOptions();
    await loadDataScopes();
  }
});

// 加载权限数据
async function loadPermissions() {
  if (!props.roleData) return;

  //console.log('开始加载权限数据，角色信息:', props.roleData);
  loading.value = true;
  try {
    // 并行获取所有权限和当前角色权限
    const [allPermissionsResponse, rolePermissionsResponse] = await Promise.all([
      fetchAllPermissions(),
      fetchRolePermissions(props.roleData.roleId)
    ]);

    // 处理所有权限数据
    //console.log('所有权限响应:', allPermissionsResponse);
    if (allPermissionsResponse && Array.isArray(allPermissionsResponse)) {
      //console.log('权限数据:', allPermissionsResponse);
      // 构建权限树数据
      buildPermissionTree(allPermissionsResponse);
    } else {
      console.error('获取所有权限失败:', allPermissionsResponse);
    }

    // 处理当前角色权限数据
    //console.log('角色权限响应:', rolePermissionsResponse);
    if (rolePermissionsResponse && Array.isArray(rolePermissionsResponse)) {
      currentPermissions.value = rolePermissionsResponse.map((p: any) => p.id);
      //console.log('当前权限ID列表:', currentPermissions.value);

      // 设置已选中的权限
      setTimeout(() => {
        if (treeRef.value) {
          // 确保首页权限始终被选中
          const homePermission = permissionTreeData.value.find(p => p.code === 'MENU_HOME');
          const permissionsToCheck = [...currentPermissions.value];
          if (homePermission && !permissionsToCheck.includes(homePermission.id)) {
            permissionsToCheck.push(homePermission.id);
          }

          //console.log('设置选中的权限ID:', permissionsToCheck);

          // 使用check-strictly=true，禁用默认父子联动，直接设置所有权限
          // 我们会通过自定义逻辑来控制联动行为
          treeRef.value.setCheckedKeys(permissionsToCheck);
        }
      }, 100);
    } else {
      console.error('获取角色权限失败:', rolePermissionsResponse);
    }
  } catch (error) {
    console.error('加载权限数据失败:', error);
    ElMessage.error('加载权限数据失败');
  } finally {
    loading.value = false;
  }
}

// 构建权限树数据
function buildPermissionTree(treePermissions: any[]) {
  //console.log('构建权限树，输入数据:', treePermissions);

  // 处理权限树数据，实现正确的权限逻辑
  const processedPermissions = treePermissions.map(permission => {
    const processedPermission = {
      ...permission,
      children: permission.children || []
    };

    // 首页权限设为必选且禁用
    if (permission.code === 'MENU_HOME') {
      processedPermission.disabled = true;
    }

    // 递归处理子权限
    if (processedPermission.children.length > 0) {
      processedPermission.children = processedPermission.children.map((child: any) => ({
        ...child,
        children: child.children || []
      }));
    }

    return processedPermission;
  });

  // 对权限进行排序，确保首页权限在最前面
  permissionTreeData.value = processedPermissions.sort((a, b) => {
    // 首页权限排在最前面
    if (a.code === 'MENU_HOME') return -1;
    if (b.code === 'MENU_HOME') return 1;

    // 其他权限按原有顺序
    return a.id - b.id;
  });

  //console.log('权限树数据设置完成:', permissionTreeData.value);
}



// 节点选中状态变化处理
function handleNodeCheck(data: any, checked: any) {
  if (!treeRef.value) return;

  // 获取当前节点信息
  const currentNode = data;
  const isChecked = checked.checkedKeys.includes(currentNode.id);

  // 处理不同类型节点的选中逻辑
  if (currentNode.type === 'menu') {
    // 判断是否为真正的目录菜单（子节点中包含其他菜单的菜单）
    const hasMenuChildren = currentNode.children && currentNode.children.some((child: any) => child.type === 'menu');

    if (hasMenuChildren) {
      // 真正的目录菜单（如"系统管理"）：保持全选/全取消逻辑
      handleDirectoryNodeCheck(currentNode, isChecked);
    } else {
      // 叶子菜单或只有按钮子节点的菜单：选中时只联动父节点，不联动按钮子节点
      handleMenuNodeCheck(currentNode, isChecked);
    }
    // 菜单节点变化后，检查父节点状态
    if (!isChecked) {
      updateParentNodeStatus(currentNode);
    }
  }
  else if (currentNode.type === 'button') {
    // 按钮类型：选中时联动父菜单，取消选中时检查是否需要取消父节点
    handleButtonNodeCheck(currentNode, isChecked);
    // 按钮节点变化后，检查父节点状态
    if (!isChecked) {
      updateParentNodeStatus(currentNode);
    }
  }
  else {
    // 其他类型：保持原有的全选/全取消逻辑
    handleDirectoryNodeCheck(currentNode, isChecked);
    // 节点取消选中时，检查父节点状态
    if (!isChecked) {
      updateParentNodeStatus(currentNode);
    }
  }

  // 确保首页权限始终被选中
  const homePermission = permissionTreeData.value.find(p => p.code === 'MENU_HOME');
  if (homePermission && treeRef.value) {
    const checkedKeys = treeRef.value.getCheckedKeys();
    if (!checkedKeys.includes(homePermission.id)) {
      treeRef.value.setChecked(homePermission.id, true, false);
    }
  }
}

// 处理菜单节点选中逻辑（叶子菜单，不联动按钮）
function handleMenuNodeCheck(menuNode: any, isChecked: boolean) {
  if (!treeRef.value) return;

  if (isChecked) {
    // 叶子菜单被选中时，只确保其父节点也被选中（向上联动），不联动子按钮
    ensureParentNodesChecked(menuNode);
  } else {
    // 叶子菜单被取消选中时，取消选中所有子节点（包括按钮）
    uncheckAllChildren(menuNode);
  }
}

// 处理按钮节点选中逻辑
function handleButtonNodeCheck(buttonNode: any, isChecked: boolean) {
  if (!treeRef.value) return;

  if (isChecked) {
    // 按钮被选中时，需要确保其父菜单也被选中（向上联动）
    ensureParentNodesChecked(buttonNode);
  }
  // 按钮被取消选中时，不需要特殊处理，让updateParentNodeStatus来处理父节点状态
}

// 处理目录节点选中逻辑
function handleDirectoryNodeCheck(directoryNode: any, isChecked: boolean) {
  if (!treeRef.value) return;

  if (isChecked) {
    // 目录被选中时，选中所有子节点（包括菜单和按钮）
    checkAllChildren(directoryNode);
    // 确保父节点也被选中
    ensureParentNodesChecked(directoryNode);
  } else {
    // 目录被取消选中时，取消选中所有子节点
    uncheckAllChildren(directoryNode);
  }
}

// 确保父节点被选中（向上联动）
function ensureParentNodesChecked(node: any) {
  if (!treeRef.value || !node.parent_code) return;

  // 查找父节点
  const parentNode = findNodeByCode(node.parent_code);
  if (parentNode) {
    // 选中父节点
    treeRef.value.setChecked(parentNode.id, true, false);
    // 递归处理父节点的父节点
    ensureParentNodesChecked(parentNode);
  }
}

// 选中所有子节点
function checkAllChildren(node: any) {
  if (!treeRef.value || !node.children) return;

  node.children.forEach((child: any) => {
    treeRef.value.setChecked(child.id, true, false);
    // 递归处理子节点的子节点
    checkAllChildren(child);
  });
}

// 取消选中所有子节点
function uncheckAllChildren(node: any) {
  if (!treeRef.value || !node.children) return;

  node.children.forEach((child: any) => {
    treeRef.value.setChecked(child.id, false, false);
    // 递归处理子节点的子节点
    uncheckAllChildren(child);
  });
}

// 更新父节点状态（根据子节点状态决定父节点是否应该被选中）
function updateParentNodeStatus(node: any) {
  if (!treeRef.value || !node.parent_code) return;

  const parentNode = findNodeByCode(node.parent_code);
  if (!parentNode || !parentNode.children) return;

  // 检查父节点的所有子节点是否都未选中
  const checkedKeys = treeRef.value.getCheckedKeys();
  const hasCheckedChildren = parentNode.children.some((child: any) =>
    checkedKeys.includes(child.id)
  );

  // 如果所有子节点都未选中，则取消选中父节点
  if (!hasCheckedChildren && checkedKeys.includes(parentNode.id)) {
    treeRef.value.setChecked(parentNode.id, false, false);
    // 递归检查父节点的父节点
    updateParentNodeStatus(parentNode);
  }
}

// 根据权限码查找节点
function findNodeByCode(code: string): any {
  function searchInNodes(nodes: any[]): any {
    for (const node of nodes) {
      if (node.code === code) {
        return node;
      }
      if (node.children && node.children.length > 0) {
        const found = searchInNodes(node.children);
        if (found) return found;
      }
    }
    return null;
  }

  return searchInNodes(permissionTreeData.value);
}

// 保存权限配置
async function handleSave() {
  if (!props.roleData) return;

  try {
    // 获取选中的权限ID（check-strictly=true模式下只有完全选中状态）
    const checkedKeys = treeRef.value.getCheckedKeys();

    // 过滤出数字ID（排除分组节点）
    const permissionIds = checkedKeys.filter((key: any) => typeof key === 'number');

    // 确保首页权限始终包含在内
    const homePermission = permissionTreeData.value.find(p => p.code === 'MENU_HOME');
    if (homePermission && !permissionIds.includes(homePermission.id)) {
      permissionIds.push(homePermission.id);
    }

    //console.log('保存权限ID列表:', permissionIds);
    //console.log('选中的权限:', checkedKeys);

    loading.value = true;

    // 保存权限
    await assignRolePermissions(props.roleData.roleId, permissionIds);

    // 保存数据范围
    const storeScopes: DataScope[] = selectedStores.value.map(storeId => ({
      store_id: storeId,
      store_type: 'offline' as const
    }));
    await setRoleDataScopes(props.roleData.roleId, storeScopes);

    ElMessage.success('权限和数据范围配置保存成功');
    visible.value = false;

    // 通知父组件刷新数据
    emit('saved');

  } catch (error) {
    console.error('保存权限配置失败:', error);
    ElMessage.error('保存权限配置失败');
  } finally {
    loading.value = false;
  }
}

// 取消操作
function handleCancel() {
  visible.value = false;
}

// 全选/取消全选
function handleCheckAll() {
  if (treeRef.value) {
    const allKeys = getAllPermissionIds();
    treeRef.value.setCheckedKeys(allKeys);
  }
}

function handleUncheckAll() {
  if (treeRef.value) {
    treeRef.value.setCheckedKeys([]);
  }
}

// 全部展开/折叠
function handleExpandAll() {
  if (treeRef.value) {
    const allKeys = getAllPermissionIds();
    allKeys.forEach(key => {
      treeRef.value.store.nodesMap[key]?.expand();
    });
  }
}

function handleCollapseAll() {
  if (treeRef.value) {
    const allKeys = getAllPermissionIds();
    allKeys.forEach(key => {
      treeRef.value.store.nodesMap[key]?.collapse();
    });
  }
}

// 获取所有权限ID
function getAllPermissionIds(): number[] {
  const ids: number[] = [];
  function traverse(nodes: any[]) {
    nodes.forEach(node => {
      if (typeof node.id === 'number') {
        ids.push(node.id);
      }
      if (node.children) {
        traverse(node.children);
      }
    });
  }
  traverse(permissionTreeData.value);
  return ids;
}

// 加载店铺选项
async function loadStoreOptions() {
  try {
    const response = await fetchOfflineStores();
    if (response && response.data) {
      storeOptions.value = response.data
        .filter(store => store.status === 1) // 只显示启用的店铺
        .map(store => ({
          id: store.id,
          name: store.name
        }));
    }
  } catch (error) {
    console.error('加载店铺选项失败:', error);
  }
}

// 加载角色数据范围
async function loadDataScopes() {
  if (!props.roleData) return;

  try {
    const response = await fetchRoleDataScopes(props.roleData.roleId);
    if (response && Array.isArray(response)) {
      currentDataScopes.value = response;
      // 设置选中的店铺
      selectedStores.value = response
        .filter(scope => scope.store_type === 'offline')
        .map(scope => scope.store_id);
    }
  } catch (error) {
    console.error('加载数据范围失败:', error);
  }
}



// 事件定义
const emit = defineEmits<{
  saved: [];
}>();
</script>

<template>
  <ElDialog
    v-model="visible"
    :title="title"
    width="700px"
    @close="visible = false"
  >
    <div v-loading="loading" class="permission-config-container">
      <ElTabs>
        <ElTabPane label="权限配置" name="permissions">
          <div class="permission-tree-container">
            <div class="tree-actions">
              <ElButton size="small" @click="handleCheckAll">全选</ElButton>
              <ElButton size="small" @click="handleUncheckAll">取消全选</ElButton>
              <ElButton size="small" @click="handleExpandAll">全部展开</ElButton>
              <ElButton size="small" @click="handleCollapseAll">全部折叠</ElButton>
            </div>

            <ElTree
              ref="treeRef"
              :data="permissionTreeData"
              :props="treeProps"
              show-checkbox
              node-key="id"
              :check-strictly="true"
              class="permission-tree"
              @check="handleNodeCheck"
            />
          </div>
        </ElTabPane>

        <ElTabPane label="数据范围" name="data-scopes">
          <div class="data-scope-container">
            <div class="scope-section">
              <h4>线下店铺数据范围</h4>
              <p class="scope-description">选择该角色可以访问的线下店铺数据</p>
              <ElCheckboxGroup v-model="selectedStores" class="store-checkbox-group">
                <ElCheckbox
                  v-for="store in storeOptions"
                  :key="store.id"
                  :label="store.id"
                  class="store-checkbox"
                >
                  {{ store.name }}
                </ElCheckbox>
              </ElCheckboxGroup>
              <div v-if="storeOptions.length === 0" class="empty-stores">
                暂无可选择的店铺
              </div>
            </div>
          </div>
        </ElTabPane>
      </ElTabs>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <ElButton @click="handleCancel">取消</ElButton>
        <ElButton type="primary" :loading="loading" @click="handleSave">
          保存
        </ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.permission-config-container {
  max-height: 600px;
  overflow-y: auto;
}

.permission-tree-container {
  min-height: 400px;
  max-height: 500px;
  overflow-y: auto;
}

.tree-actions {
  margin-bottom: 16px;
  display: flex;
  gap: 8px;
}

.permission-tree {
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding: 16px;
  background: var(--el-bg-color-page);
}

.data-scope-container {
  padding: 16px 0;
}

.scope-section {
  margin-bottom: 24px;
}

.scope-section h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.scope-description {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #606266;
}

.store-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.store-checkbox {
  margin-right: 0;
  margin-bottom: 0;
}

.empty-stores {
  color: #909399;
  font-size: 14px;
  text-align: center;
  padding: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

:deep(.el-tabs__content) {
  padding-top: 16px;
}
</style>
