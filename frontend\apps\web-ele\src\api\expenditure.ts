import { requestClient } from '#/api/request';

export interface ExpenditureCategory {
  id: number;
  name: string;
  status: number;
  created_at: string;
  updated_at: string;
}

export interface ExpenditureSearchParams {
  page?: number;
  pageSize?: number;
  name?: string;
  status?: string;
}

export interface ExpenditureCreateData {
  name: string;
  status: number;
}

export interface ExpenditureUpdateData {
  name: string;
  status: number;
}

// 获取支出分类列表
export async function fetchExpenditureList(params: ExpenditureSearchParams) {
  return requestClient.get<{
    data: ExpenditureCategory[];
    total: number;
    page: number;
    pageSize: number;
  }>('/expenditure-categories', {
    params,
    responseReturn: 'body'
  });
}

// 创建支出分类
export async function addExpenditure(data: ExpenditureCreateData) {
  return requestClient.post<{
    data: ExpenditureCategory;
    message: string;
  }>('/expenditure-categories', data);
}

// 更新支出分类
export async function updateExpenditure(id: number, data: ExpenditureUpdateData) {
  return requestClient.put<{
    message: string;
  }>(`/expenditure-categories/${id}`, data);
}

// 删除支出分类
export async function deleteExpenditure(id: number) {
  return requestClient.delete<{
    message: string;
  }>(`/expenditure-categories/${id}`);
}

// 批量删除支出分类
export async function batchDeleteExpenditure(ids: number[]) {
  return requestClient.post<{
    message: string;
  }>('/expenditure-categories/batch-delete', { ids });
}

// 更新支出分类状态
export async function updateExpenditureStatus(id: number, status: boolean) {
  return requestClient.put<{
    message: string;
  }>(`/expenditure-categories/${id}/status`, { status: status ? 1 : 0 });
}
