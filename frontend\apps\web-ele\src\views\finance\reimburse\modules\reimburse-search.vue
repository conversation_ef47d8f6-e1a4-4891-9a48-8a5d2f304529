<script setup lang="ts">
import { ref, onMounted } from 'vue';
import {
  ElCard,
  ElCollapse,
  ElCollapseItem,
  ElForm,
  ElFormItem,
  ElRow,
  ElCol,
  ElInput,
  ElDatePicker,
  ElSelect,
  ElOption,
  ElButton,
  ElSpace
} from 'element-plus';
import type { CollapseModelValue } from 'element-plus';
import { fetchOfflineStores } from '../../../../api/finance';
import type { Store } from '../../../../api/finance';
import { useDataScope } from '../../../../composables/useDataScope';

defineOptions({ name: 'FinanceSearch' });

// 定义props，v-model:model类型为{dateRange, shop, status}
interface SearchModel {
  dateRange: string[];
  shop: string;
  status: string;
}
const model = defineModel<SearchModel>('model', { required: true });

interface Emits {
  (e: 'reset'): void;
  (e: 'search'): void;
  (e: 'collapse-change', val: boolean): void;
}
const emit = defineEmits<Emits>();

const isCollapse = ref(false); // 是否折叠

// 数据范围管理
const { filterAccessibleStoreOptions } = useDataScope();

// 店铺选项
const storeOptions = ref<Array<{ label: string; value: string }>>([]);
// 状态选项
const statusOptions = ref<Array<{ label: string; value: string }>>([
  { label: '全部', value: '' },
  { label: '未报销', value: '0' },
  { label: '已报销', value: '1' }
]);

function reset() {
  emit('reset');
}

function search() {
  emit('search');
}

function handleCollapseChange(val: CollapseModelValue) {
  // val 可能为 string 或 string[]
  let isCollapsed = true;
  if (Array.isArray(val)) {
    isCollapsed = !val.includes('order-search');
  } else if (typeof val === 'string') {
    isCollapsed = val !== 'order-search';
  }
  isCollapse.value = isCollapsed;
  emit('collapse-change', isCollapsed);
}

// 加载店铺选项
async function loadStoreOptions() {
  try {
    const response = await fetchOfflineStores();
    if (response && response.data) {
      const allStoreOptions = [
        { label: '全部', value: '' }, // 添加全部选项
        ...response.data
          .filter(store => store.status === 1) // 只显示启用的店铺
          .map(store => ({
            label: store.name,
            value: store.name
          }))
      ];

      // 根据用户数据范围过滤店铺选项
      storeOptions.value = filterAccessibleStoreOptions(allStoreOptions, 'offline');
    }
  } catch (error) {
    console.error('加载店铺选项失败:', error);
  }
}

// 组件挂载时加载选项
onMounted(() => {
  loadStoreOptions();
});
</script>

<template>
  <ElCard class="card-wrapper">
    <ElCollapse @change="handleCollapseChange">
      <ElCollapseItem title="搜索" name="order-search">
        <ElForm :model="model" label-position="right" :label-width="80">
          <ElRow :gutter="24">
            <!-- 日期范围 -->
            <ElCol :lg="6" :md="12" :sm="24">
              <ElFormItem label="日期" prop="dateRange">
                <ElDatePicker
                  v-model="model.dateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  clearable
                  style="width: 100%"
                />
              </ElFormItem>
            </ElCol>
            <!-- 店铺 -->
            <ElCol :lg="5" :md="12" :sm="24">
              <ElFormItem label="店铺" prop="shop">
                <ElSelect v-model="model.shop" placeholder="请选择店铺" style="width: 100%" clearable filterable>
                  <ElOption
                    v-for="option in storeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <!-- 状态 -->
            <ElCol :lg="5" :md="12" :sm="24">
              <ElFormItem label="状态" prop="status">
                <ElSelect v-model="model.status" placeholder="请选择状态" clearable style="width: 100%">
                  <ElOption
                    v-for="option in statusOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </ElSelect>
              </ElFormItem>
            </ElCol>
            <!-- 操作按钮 -->
            <ElCol :lg="3" :md="12" :sm="24" class="flex justify-end items-center">
              <ElSpace>
                <ElButton @click="reset">重置</ElButton>
                <ElButton type="primary" plain @click="search">搜索</ElButton>
              </ElSpace>
            </ElCol>
          </ElRow>
        </ElForm>
      </ElCollapseItem>
    </ElCollapse>
  </ElCard>
</template>

<style scoped>
.card-wrapper {
  margin-bottom: 0;
}

.card-wrapper :deep(.el-card__body) {
  padding: 16px;
}

.card-wrapper :deep(.el-collapse) {
  border: none;
}

.card-wrapper :deep(.el-collapse-item__header) {
  background-color: transparent;
  border: none;
  padding: 0 0 12px 0;
  font-weight: 500;
  font-size: 14px;
}

.card-wrapper :deep(.el-collapse-item__wrap) {
  border: none;
  background-color: transparent;
}

.card-wrapper :deep(.el-collapse-item__content) {
  padding: 0;
  background-color: transparent;
}

/* 确保动画效果正确 */
.card-wrapper :deep(.el-collapse-item) {
  border: none;
}

.card-wrapper :deep(.el-collapse-item__arrow) {
  margin: 0 8px 0 0;
}

/* 表单样式优化 */
.card-wrapper :deep(.el-form-item) {
  margin-bottom: 16px;
}

.card-wrapper :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

/* 日期选择器样式 */
.card-wrapper :deep(.el-date-editor) {
  width: 100%;
}

/* 按钮样式保持默认 */

/* 输入框样式 */
.card-wrapper :deep(.el-input__wrapper) {
  border-radius: 6px;
  transition: all 0.3s;
}

.card-wrapper :deep(.el-input__wrapper:hover) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
