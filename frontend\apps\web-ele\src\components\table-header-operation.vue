<script setup lang="ts">
import { ref } from 'vue';
import { ElButton, ElPopconfirm } from 'element-plus';
import { createIconifyIcon } from '@vben/icons';

// 创建图标组件
const PlusIcon = createIconifyIcon('mdi:plus');
const DeleteIcon = createIconifyIcon('mdi:delete');
const RefreshIcon = createIconifyIcon('mdi:refresh');

interface Props {
  disabledDelete?: boolean;
  loading?: boolean;
  showAdd?: boolean;
  showBatchDelete?: boolean;
  deleteConfirmTitle?: string;
  addButtonText?: string;
  deleteButtonText?: string;
  refreshButtonText?: string;
  // 支持自定义操作按钮
  showCustomButtons?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  disabledDelete: true,
  loading: false,
  showAdd: true,
  showBatchDelete: true,
  deleteConfirmTitle: '确认批量删除选中的记录吗？',
  addButtonText: '新增',
  deleteButtonText: '批量删除',
  refreshButtonText: '刷新',
  showCustomButtons: false
});

const emit = defineEmits<{
  add: [];
  delete: [];
  refresh: [];
}>();

function handleAdd() {
  emit('add');
}

function handleDelete() {
  emit('delete');
}

function handleRefresh() {
  emit('refresh');
}
</script>

<template>
  <div class="flex items-center gap-2">
    <el-button v-if="props.showAdd" type="primary" @click="handleAdd">
      <template #icon>
        <PlusIcon />
      </template>
      {{ props.addButtonText }}
    </el-button>

    <!-- 自定义按钮插槽 -->
    <slot name="custom-buttons" v-if="props.showCustomButtons"></slot>

    <el-popconfirm
      v-if="props.showBatchDelete"
      :title="props.deleteConfirmTitle"
      @confirm="handleDelete"
    >
      <template #reference>
        <el-button
          type="danger"
          :disabled="props.disabledDelete"
        >
          <template #icon>
            <DeleteIcon />
          </template>
          {{ props.deleteButtonText }}
        </el-button>
      </template>
    </el-popconfirm>
    <el-button
      :loading="props.loading"
      @click="handleRefresh"
    >
      <template #icon>
        <RefreshIcon />
      </template>
      {{ props.refreshButtonText }}
    </el-button>
  </div>
</template>

<style scoped>
/* 组件样式 */
</style>
