#!/usr/bin/env python3
"""验证finance表数据导入结果"""

import psycopg2
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config.config import Config

def verify_finance_data():
    """验证finance表中的数据"""
    try:
        # 连接数据库
        conn = psycopg2.connect(**Config.DATABASE_CONFIG)
        cursor = conn.cursor()
        
        # 查询总记录数
        cursor.execute('SELECT COUNT(*) FROM finance')
        total_count = cursor.fetchone()[0]
        print(f"📊 finance表中共有 {total_count} 条记录")
        
        # 查询前5条记录
        cursor.execute('''
            SELECT shop, record_date, type, category, amount, description 
            FROM finance 
            ORDER BY id 
            LIMIT 5
        ''')
        rows = cursor.fetchall()
        
        print("\n📋 前5条记录:")
        print("-" * 80)
        for i, row in enumerate(rows, 1):
            print(f"{i}. 店铺: {row[0]}")
            print(f"   日期: {row[1]}")
            print(f"   类型: {row[2]}")
            print(f"   分类: {row[3]}")
            print(f"   金额: {row[4]}")
            print(f"   描述: {row[5]}")
            print("-" * 40)
        
        # 统计各店铺的记录数
        cursor.execute('''
            SELECT shop, COUNT(*) as count 
            FROM finance 
            GROUP BY shop 
            ORDER BY count DESC
        ''')
        shop_stats = cursor.fetchall()
        
        print("\n🏪 各店铺记录统计:")
        print("-" * 30)
        for shop, count in shop_stats:
            print(f"{shop}: {count} 条")
        
        # 统计收入支出
        cursor.execute('''
            SELECT type, COUNT(*) as count, SUM(amount) as total_amount
            FROM finance 
            GROUP BY type
        ''')
        type_stats = cursor.fetchall()
        
        print("\n💰 收支统计:")
        print("-" * 40)
        for type_name, count, total_amount in type_stats:
            print(f"{type_name}: {count} 条记录, 总金额: {total_amount:.2f}")
        
        conn.close()
        print("\n✅ 数据验证完成")
        
    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")

if __name__ == "__main__":
    verify_finance_data()
